{"name": "sabicord-music", "version": "2.0.0", "description": "SabiCord - A modern TypeScript Discord music bot with Lavalink integration", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "docker:build": "docker build -t sabicord-music .", "docker:run": "docker-compose up -d"}, "keywords": ["discord", "bot", "music", "lavalink", "typescript", "nodejs"], "author": "Vocard Development", "license": "MIT", "dependencies": {"@discordx/di": "^3.3.4", "@discordx/importer": "^1.3.3", "@discordx/pagination": "^3.4.1", "axios": "^1.6.5", "canvas": "^3.1.2", "discord.js": "^14.14.1", "discordx": "^11.12.5", "dotenv": "^16.3.1", "humanize-duration": "^3.31.0", "mongodb": "^6.3.0", "reflect-metadata": "^0.2.1", "tsyringe": "^4.8.0", "validator": "^13.11.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "ws": "^8.16.0"}, "devDependencies": {"@types/jest": "^29.5.11", "@types/node": "^20.11.5", "@types/validator": "^13.11.8", "@types/ws": "^8.5.10", "@typescript-eslint/eslint-plugin": "^6.19.0", "@typescript-eslint/parser": "^6.19.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.7.0", "prettier": "^3.2.4", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/ChocoMeow/Vocard.git"}, "bugs": {"url": "https://github.com/ChocoMeow/Vocard/issues"}, "homepage": "https://github.com/ChocoMeow/Vocard#readme"}
# ------------------------------------------------------------------------------------------------------------ #

# READ THIS BEFORE INSTALL!

# This is a docker-compose file for running Vocard with Lavalink and MongoDB.
# In order to run this, you need to have Dock<PERSON> and Docker Compose installed.
# You can install Docker from https://docs.docker.com/get-docker/
# and Docker Compose from https://docs.docker.com/compose/install/

# Step 1: Start the installation by creating the future config directory for Vocard.
# example - `root@docker:~# mkdir -p /opt/vocard/config`

# Use `cd` to navigate to the config directory.
# example - `root@docker:~# cd /opt/vocard/config`

# Step 3: Choose installation method: Build the image from the Dockerfile or pull it from GitHub(recommended).
# If you chose to pull from <PERSON>er Hub, comment the "build" lines and uncomment the "image" line.
# If you chose to build the image from the Dockerfile, do the following:
# uncomment this
#       build:
#           dockerfile: ./Dockerfile
# and comment this
#       image: ghcr.io/chocomeow/vocard:latest
# example - `root@docker:/opt/vocard/config# wget https://github.com/ChocoMeow/Vocard/archive/refs/heads/main.zip`

# Step 4: Configure application.yml and settings.json in the config directory.
# In order to avoid silly syntax errors it is recommended to use external code editor such as VS Code or Notepad++.
# Then you can upload files to host using tools such as WinSCP or
# using `nano` to create and edit the files directly using hosts terminal.
# NOTE that some terminals DO NOT let you paste, so you can either use WinSCP or SSH app like Putty.

# example - `root@docker:/opt/vocard/config# nano application.yml`
# example - `root@docker:/opt/vocard/config# nano settings.json`
# To exit nano, press `Ctrl + S`, then `Ctrl + X` to save changes.

# Step 5: If the values are set correctly, you can start the installation by running the following command
# example - `root@docker:/opt/vocard/config# docker-compose up -d`   (could be `docker compose` on some systems)

# ------------------------------------------ THANK YOU FOR READING! ------------------------------------------ #
name: vocard
services:
    lavalink:
        container_name: lavalink
        # image: ghcr.io/lavalink-devs/lavalink:latest
        # Use build to run Lavalink on the latest JRE 23 version (for better performance)
        build:
            context: ./lavalink
            dockerfile: ./Dockerfile-lavalink
        restart: unless-stopped
        environment:
            - _JAVA_OPTIONS=-Xmx1G
            - SERVER_PORT=2333
            # there is no point in changing the password here, since the container is available only in docker network
            - LAVALINK_SERVER_PASSWORD=youshallnotpass # Change password if needed (don't forget to change it in healthcheck below and settings.json)
        volumes:
            - ./lavalink/application.yml:/opt/Lavalink/application.yml
        networks:
            - vocard
        expose:
            - "2333"
        healthcheck:
            test: nc -z -v localhost 2333
            interval: 10s
            timeout: 5s
            retries: 5

    vocard-db:
       container_name: vocard-db
       image: mongo:8
       restart: unless-stopped
       volumes:
           - ./data/mongo/db:/data/db
           - ./data/mongo/conf:/data/configdb
       environment:
           - MONGO_INITDB_ROOT_USERNAME=admin # For your MongoDB URL use "*************************************"
           - MONGO_INITDB_ROOT_PASSWORD=admin
       expose:
           - "27017"
       networks:
           - vocard
       command: ["mongod", "--oplogSize=1024", "--wiredTigerCacheSizeGB=1", "--auth", "--noscripting"]
       healthcheck:
            test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
            interval: 10s
            timeout: 5s
            retries: 5
            start_period: 10s
            

    # vocard-dashboard: 
    #     container_name: vocard-dashboard
    #     image: ghcr.io/chocomeow/vocard-dashboard:latest
    #     restart: unless-stopped
    #     # If you want to build the image from the Dockerfile, uncomment the "build" lines and comment the "image" line.
    #     # build:
    #     #     context: ./dashboard
    #     #     dockerfile: ./Dockerfile
    #     volumes:
    #         - ./dashboard/settings.json:/app/settings.json
    #     ports:
    #         - 8000:8000
    #     networks:
    #         - vocard
    #         - web

    vocard:
        container_name: vocard
        restart: unless-stopped
        # If you want to build the image from the Dockerfile, uncomment the "build" lines and comment the "image" line.
        # image: ghcr.io/chocomeow/vocard:latest
        build:
            dockerfile: ./Dockerfile
        volumes:
            - ./config/.env:/app/.env:ro
            - ./logs:/app/logs
        environment:
            - NODE_ENV=production
            - DISCORD_TOKEN=${DISCORD_TOKEN}
            - DISCORD_CLIENT_ID=${DISCORD_CLIENT_ID}
            - MONGODB_URL=*************************************
            - MONGODB_NAME=vocard
            - LAVALINK_HOST=lavalink
            - LAVALINK_PORT=2333
            - LAVALINK_PASSWORD=youshallnotpass
            - LAVALINK_SECURE=false
        networks:
            - vocard
        depends_on:
            lavalink:
                condition: service_healthy
            # vocard-dashboard:
            #     condition: service_started
            vocard-db:
                condition: service_healthy
        healthcheck:
            test: ["CMD", "node", "-e", "process.exit(0)"]
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 40s

networks:
    vocard:
        name: vocard

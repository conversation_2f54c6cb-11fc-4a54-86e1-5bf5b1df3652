{"env": {"es2022": true, "node": true}, "extends": ["eslint:recommended", "@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module", "project": "./tsconfig.json"}, "plugins": ["@typescript-eslint", "prettier"], "rules": {"prettier/prettier": "error", "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/explicit-function-return-type": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/prefer-const": "error", "@typescript-eslint/no-var-requires": "error", "prefer-const": "error", "no-var": "error", "no-console": "warn", "eqeqeq": "error", "curly": "error"}, "ignorePatterns": ["dist/", "node_modules/", "*.js"]}
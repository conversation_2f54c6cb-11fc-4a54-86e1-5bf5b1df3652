/**
 * MIT License
 *
 * Copyright (c) 2025 NirrussVn0
 */

import 'reflect-metadata';
import { MusicClient } from './core/MusicClient';
import { logger } from './core/Logger';

// Node.js globals - using dynamic imports to avoid TypeScript issues
const nodeProcess = eval('process');
const nodeConsole = eval('console');

async function main(): Promise<void> {
  try {
    logger.info('Starting Vocard Discord Music Bot...', 'main');
    
    const client = new MusicClient();
    await client.start();
    
  } catch (error) {
    logger.error('Failed to start bot', error as Error, 'main');
    nodeProcess.exit(1);
  }
}

// Start the bot
main().catch((error) => {
  nodeConsole.error('Fatal error:', error);
  nodeProcess.exit(1);
});

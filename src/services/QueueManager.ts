/**
 * MIT License
 * 
 * Copyright (c) 2025 NirrussVn0
 */

import { Track } from '../voicelink/Track';
import { Queue, FairQueue } from '../voicelink/Queue';
import { LoopType } from '../voicelink/Enums';
import { ILogger } from '../core/Logger';

/**
 * Queue manager following SRP
 * Responsible only for managing the music queue
 */
export class QueueManager {
  private queue: Queue;
  private logger: ILogger;
  private loopMode: LoopType = LoopType.NONE;
  private previousTrack: Track | null = null;

  constructor(queueType: 'Queue' | 'FairQueue', maxSize: number, logger: ILogger) {
    this.queue = queueType === 'FairQueue' ? new FairQueue(maxSize) : new Queue(maxSize);
    this.logger = logger;
  }

  /**
   * Add a single track to the queue
   */
  public addTrack(track: Track): void {
    try {
      this.queue.add(track);
      this.logger.debug(`Track added to queue: ${track.title}`, 'queue');
    } catch (error) {
      this.logger.error('Failed to add track to queue', error as Error, 'queue');
      throw error;
    }
  }

  /**
   * Add multiple tracks to the queue
   */
  public addTracks(tracks: Track[]): void {
    try {
      for (const track of tracks) {
        this.queue.add(track);
      }
      this.logger.debug(`${tracks.length} tracks added to queue`, 'queue');
    } catch (error) {
      this.logger.error('Failed to add tracks to queue', error as Error, 'queue');
      throw error;
    }
  }

  /**
   * Remove a track at specific index
   */
  public removeTrack(index: number): Track | null {
    try {
      const track = this.queue.remove(index);
      if (track) {
        this.logger.debug(`Track removed from queue: ${track.title}`, 'queue');
      }
      return track;
    } catch (error) {
      this.logger.error('Failed to remove track from queue', error as Error, 'queue');
      return null;
    }
  }

  /**
   * Get the next track to play
   */
  public getNext(): Track | null {
    try {
      let nextTrack: Track | null = null;

      switch (this.loopMode) {
        case LoopType.TRACK:
          nextTrack = this.previousTrack;
          break;
        
        case LoopType.QUEUE:
          nextTrack = this.queue.poll();
          if (nextTrack && this.previousTrack) {
            this.queue.add(this.previousTrack);
          }
          break;
        
        case LoopType.NONE:
        default:
          nextTrack = this.queue.poll();
          break;
      }

      if (nextTrack) {
        this.previousTrack = nextTrack;
        this.logger.debug(`Next track: ${nextTrack.title}`, 'queue');
      }

      return nextTrack;
    } catch (error) {
      this.logger.error('Failed to get next track', error as Error, 'queue');
      return null;
    }
  }

  /**
   * Clear the entire queue
   */
  public clear(): void {
    try {
      this.queue.clear();
      this.previousTrack = null;
      this.logger.debug('Queue cleared', 'queue');
    } catch (error) {
      this.logger.error('Failed to clear queue', error as Error, 'queue');
    }
  }

  /**
   * Shuffle the queue
   */
  public shuffle(): void {
    try {
      this.queue.shuffle();
      this.logger.debug('Queue shuffled', 'queue');
    } catch (error) {
      this.logger.error('Failed to shuffle queue', error as Error, 'queue');
    }
  }

  /**
   * Get queue size
   */
  public size(): number {
    return this.queue.size();
  }

  /**
   * Check if queue is empty
   */
  public isEmpty(): boolean {
    return this.queue.isEmpty();
  }

  /**
   * Get all tracks in queue
   */
  public getTracks(): Track[] {
    return this.queue.getTracks();
  }

  /**
   * Set loop mode
   */
  public setLoopMode(mode: LoopType): void {
    this.loopMode = mode;
    this.logger.debug(`Loop mode set to: ${LoopType[mode]}`, 'queue');
  }

  /**
   * Get current loop mode
   */
  public getLoopMode(): LoopType {
    return this.loopMode;
  }

  /**
   * Get current track
   */
  public getCurrentTrack(): Track | null {
    return this.previousTrack;
  }

  /**
   * Peek at next track without removing it
   */
  public peekNext(): Track | null {
    return this.queue.peek();
  }

  /**
   * Get queue position of a track
   */
  public getTrackPosition(track: Track): number {
    const tracks = this.queue.getTracks();
    return tracks.findIndex(t => t.identifier === track.identifier);
  }

  /**
   * Move track to different position
   */
  public moveTrack(fromIndex: number, toIndex: number): boolean {
    try {
      const track = this.removeTrack(fromIndex);
      if (track) {
        this.queue.addAt(toIndex, track);
        this.logger.debug(`Track moved from ${fromIndex} to ${toIndex}`, 'queue');
        return true;
      }
      return false;
    } catch (error) {
      this.logger.error('Failed to move track', error as Error, 'queue');
      return false;
    }
  }
}

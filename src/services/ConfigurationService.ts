/**
 * MIT License
 * 
 * Copyright (c) 2025 NirrussVn0
 */

import { Settings } from '../core/Settings';
import { IDiscordConfig, IDatabaseConfig, IBotConfig } from '../interfaces/ISettings';

/**
 * Configuration service following SRP
 * Responsible only for loading and validating configuration
 */
export class ConfigurationService {
  private settings: Settings;

  constructor() {
    this.settings = new Settings();
  }

  /**
   * Load and validate all configuration
   */
  public async loadConfiguration(): Promise<void> {
    this.settings.validate();
  }

  /**
   * Get Discord configuration
   */
  public getDiscordConfig(): IDiscordConfig {
    return {
      token: this.settings.token,
      client_id: this.settings.client_id,
    };
  }

  /**
   * Get database configuration
   */
  public getDatabaseConfig(): IDatabaseConfig {
    return {
      mongodb_url: this.settings.mongodb_url,
      mongodb_name: this.settings.mongodb_name,
    };
  }

  /**
   * Get bot configuration
   */
  public getBotConfig(): IBotConfig {
    return {
      prefix: this.settings.prefix,
      embed_color: this.settings.embed_color,
      default_max_queue: this.settings.default_max_queue,
      lyrics_platform: this.settings.lyrics_platform,
      bot_access_user: this.settings.bot_access_user,
      cooldowns: this.settings.cooldowns,
      aliases: this.settings.aliases,
    };
  }

  /**
   * Get logging configuration
   */
  public getLoggingConfig() {
    return this.settings.logging;
  }

  /**
   * Get full settings (for backward compatibility)
   */
  public getSettings(): Settings {
    return this.settings;
  }
}

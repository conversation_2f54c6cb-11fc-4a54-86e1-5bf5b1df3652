/**
 * MIT License
 * 
 * Copyright (c) 2025 NirrussVn0
 */

import { PlayerState } from '../voicelink/Enums';
import { ILogger } from '../core/Logger';
import { EventEmitter } from 'events';

/**
 * Player state manager following SRP
 * Responsible only for managing player state transitions
 */
export class PlayerStateManager extends EventEmitter {
  private currentState: PlayerState = PlayerState.IDLE;
  private logger: ILogger;
  private stateHistory: Array<{ state: PlayerState; timestamp: number }> = [];
  private readonly maxHistorySize = 10;

  constructor(logger: ILogger) {
    super();
    this.logger = logger;
    this.recordStateChange(PlayerState.IDLE);
  }

  /**
   * Set player state with validation
   */
  public setState(newState: PlayerState): void {
    const previousState = this.currentState;
    
    if (!this.isValidStateTransition(previousState, newState)) {
      this.logger.warn(
        `Invalid state transition from ${PlayerState[previousState]} to ${PlayerState[newState]}`,
        'player-state'
      );
      return;
    }

    this.currentState = newState;
    this.recordStateChange(newState);
    
    this.logger.debug(
      `Player state changed: ${PlayerState[previousState]} -> ${PlayerState[newState]}`,
      'player-state'
    );

    this.emit('stateChange', newState, previousState);
  }

  /**
   * Get current player state
   */
  public getState(): PlayerState {
    return this.currentState;
  }

  /**
   * Check if player is currently playing
   */
  public isPlaying(): boolean {
    return this.currentState === PlayerState.PLAYING;
  }

  /**
   * Check if player is paused
   */
  public isPaused(): boolean {
    return this.currentState === PlayerState.PAUSED;
  }

  /**
   * Check if player is connected
   */
  public isConnected(): boolean {
    return this.currentState !== PlayerState.IDLE && 
           this.currentState !== PlayerState.DESTROYED;
  }

  /**
   * Check if player can start playing
   */
  public canPlay(): boolean {
    return this.currentState === PlayerState.IDLE || 
           this.currentState === PlayerState.PAUSED ||
           this.currentState === PlayerState.STOPPED;
  }

  /**
   * Check if player can be paused
   */
  public canPause(): boolean {
    return this.currentState === PlayerState.PLAYING;
  }

  /**
   * Check if player can be stopped
   */
  public canStop(): boolean {
    return this.currentState === PlayerState.PLAYING || 
           this.currentState === PlayerState.PAUSED;
  }

  /**
   * Get state history
   */
  public getStateHistory(): Array<{ state: PlayerState; timestamp: number }> {
    return [...this.stateHistory];
  }

  /**
   * Force state change (for emergency situations)
   */
  public forceState(state: PlayerState): void {
    const previousState = this.currentState;
    this.currentState = state;
    this.recordStateChange(state);
    
    this.logger.warn(
      `Player state forcibly changed: ${PlayerState[previousState]} -> ${PlayerState[state]}`,
      'player-state'
    );

    this.emit('stateChange', state, previousState);
  }

  /**
   * Reset to idle state
   */
  public reset(): void {
    this.setState(PlayerState.IDLE);
    this.stateHistory = [];
    this.recordStateChange(PlayerState.IDLE);
  }

  /**
   * Validate state transitions
   */
  private isValidStateTransition(from: PlayerState, to: PlayerState): boolean {
    const validTransitions: Record<PlayerState, PlayerState[]> = {
      [PlayerState.IDLE]: [PlayerState.CONNECTING, PlayerState.DESTROYED],
      [PlayerState.CONNECTING]: [PlayerState.IDLE, PlayerState.STOPPED, PlayerState.DESTROYED],
      [PlayerState.PLAYING]: [PlayerState.PAUSED, PlayerState.STOPPED, PlayerState.DESTROYED],
      [PlayerState.PAUSED]: [PlayerState.PLAYING, PlayerState.STOPPED, PlayerState.DESTROYED],
      [PlayerState.STOPPED]: [PlayerState.PLAYING, PlayerState.IDLE, PlayerState.DESTROYED],
      [PlayerState.DESTROYED]: [], // No transitions from destroyed state
    };

    return validTransitions[from]?.includes(to) ?? false;
  }

  /**
   * Record state change in history
   */
  private recordStateChange(state: PlayerState): void {
    this.stateHistory.push({
      state,
      timestamp: Date.now(),
    });

    // Keep only recent history
    if (this.stateHistory.length > this.maxHistorySize) {
      this.stateHistory.shift();
    }
  }
}

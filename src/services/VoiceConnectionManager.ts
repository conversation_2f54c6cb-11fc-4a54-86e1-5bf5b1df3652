/**
 * MIT License
 * 
 * Copyright (c) 2025 NirrussVn0
 */

import { 
  VoiceChannel, 
  VoiceConnection,
  joinVoiceChannel,
  VoiceConnectionStatus,
  entersState,
  DiscordGatewayAdapterCreator
} from 'discord.js';
import { EventEmitter } from 'events';
import { ILogger } from '../core/Logger';

/**
 * Voice connection manager following SRP
 * Responsible only for managing Discord voice connections
 */
export class VoiceConnectionManager extends EventEmitter {
  private connection: VoiceConnection | null = null;
  private logger: ILogger;
  private reconnectAttempts = 0;
  private readonly maxReconnectAttempts = 5;

  constructor(logger: ILogger) {
    super();
    this.logger = logger;
  }

  /**
   * Connect to a voice channel
   */
  public async connect(channel: VoiceChannel): Promise<void> {
    try {
      if (this.connection && this.connection.state.status !== VoiceConnectionStatus.Destroyed) {
        this.logger.warn(`Already connected to voice channel`, 'voice');
        return;
      }

      this.connection = joinVoiceChannel({
        channelId: channel.id,
        guildId: channel.guild.id,
        adapterCreator: channel.guild.voiceAdapterCreator as DiscordGatewayAdapterCreator,
        selfDeaf: true,
        selfMute: false,
      });

      this.setupConnectionHandlers();

      // Wait for connection to be ready
      await entersState(this.connection, VoiceConnectionStatus.Ready, 30_000);
      
      this.logger.info(`Connected to voice channel: ${channel.name}`, 'voice');
      this.emit('connected', channel);
      
    } catch (error) {
      this.logger.error('Failed to connect to voice channel', error as Error, 'voice');
      throw error;
    }
  }

  /**
   * Disconnect from voice channel
   */
  public async disconnect(): Promise<void> {
    if (!this.connection) {
      return;
    }

    try {
      this.connection.destroy();
      this.connection = null;
      this.reconnectAttempts = 0;
      
      this.logger.info('Disconnected from voice channel', 'voice');
      this.emit('disconnected');
      
    } catch (error) {
      this.logger.error('Error during voice disconnection', error as Error, 'voice');
    }
  }

  /**
   * Check if connected to voice
   */
  public isConnected(): boolean {
    return this.connection !== null && 
           this.connection.state.status === VoiceConnectionStatus.Ready;
  }

  /**
   * Get the voice connection
   */
  public getConnection(): VoiceConnection | null {
    return this.connection;
  }

  /**
   * Setup connection event handlers
   */
  private setupConnectionHandlers(): void {
    if (!this.connection) return;

    this.connection.on(VoiceConnectionStatus.Disconnected, async () => {
      try {
        await Promise.race([
          entersState(this.connection!, VoiceConnectionStatus.Signalling, 5_000),
          entersState(this.connection!, VoiceConnectionStatus.Connecting, 5_000),
        ]);
      } catch (error) {
        this.handleReconnection();
      }
    });

    this.connection.on(VoiceConnectionStatus.Destroyed, () => {
      this.connection = null;
      this.emit('destroyed');
    });

    this.connection.on('error', (error) => {
      this.logger.error('Voice connection error', error, 'voice');
      this.emit('error', error);
    });
  }

  /**
   * Handle reconnection logic
   */
  private async handleReconnection(): Promise<void> {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.logger.error('Max reconnection attempts reached', undefined, 'voice');
      this.disconnect();
      return;
    }

    this.reconnectAttempts++;
    this.logger.warn(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})`, 'voice');
    
    try {
      if (this.connection) {
        this.connection.destroy();
      }
      this.emit('reconnecting', this.reconnectAttempts);
    } catch (error) {
      this.logger.error('Reconnection failed', error as Error, 'voice');
    }
  }
}

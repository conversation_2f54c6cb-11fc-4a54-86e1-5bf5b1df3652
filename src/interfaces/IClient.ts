/**
 * MIT License
 * 
 * Copyright (c) 2025 NirrussVn0
 */

import { Guild, User, VoiceChannel } from 'discord.js';

/**
 * Interface for Discord client abstraction
 */
export interface IDiscordClient {
  start(): Promise<void>;
  shutdown(): Promise<void>;
  isReady(): boolean;
}

/**
 * Interface for service initialization
 */
export interface IServiceInitializer {
  initializeServices(): Promise<void>;
  registerServices(): void;
}

/**
 * Interface for event handling
 */
export interface IEventHandler {
  setupEventHandlers(): void;
  onReady(): Promise<void>;
  onInteractionCreate(interaction: any): Promise<void>;
  onMessageCreate(message: any): Promise<void>;
}

/**
 * Interface for command management
 */
export interface ICommandManager {
  importCommands(): Promise<void>;
  syncCommands(): Promise<void>;
}

/**
 * Interface for music functionality
 */
export interface IMusicService {
  createPlayer(guild: Guild, channel: VoiceChannel): Promise<any>;
  getPlayer(guildId: string): any | null;
  destroyPlayer(guildId: string): Promise<void>;
}

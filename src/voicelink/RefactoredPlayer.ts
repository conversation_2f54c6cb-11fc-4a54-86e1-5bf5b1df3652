/**
 * MIT License
 * 
 * Copyright (c) 2025 NirrussVn0
 */

import { VoiceChannel, Guild, User } from 'discord.js';
import { EventEmitter } from 'events';
import { Track } from './Track';
import { Queue } from './Queue';
import { Filters } from './Filters';
import { Node } from './Node';
import { LoopType, PlayerState, TrackEndReason } from './Enums';
import { ILogger } from '../core/Logger';
import { VoiceConnectionManager } from '../services/VoiceConnectionManager';
import { AudioEffectsManager } from '../services/AudioEffectsManager';
import { QueueManager } from '../services/QueueManager';
import { PlayerStateManager } from '../services/PlayerStateManager';

/**
 * Player options interface
 */
export interface IPlayerOptions {
  guild: Guild;
  channel: VoiceChannel;
  node?: Node;
  volume?: number;
  maxQueueSize?: number;
  allowDuplicate?: boolean;
  queueType?: 'Queue' | 'FairQueue';
}

/**
 * Refactored Player class following SOLID principles
 * Now uses composition and dependency injection
 */
export class RefactoredPlayer extends EventEmitter {
  // Core properties
  public readonly guild: Guild;
  public readonly channel: VoiceChannel;
  public readonly node: Node;
  
  // Service dependencies
  private readonly logger: ILogger;
  private readonly voiceManager: VoiceConnectionManager;
  private readonly audioManager: AudioEffectsManager;
  private readonly queueManager: QueueManager;
  private readonly stateManager: PlayerStateManager;
  
  // Configuration
  private readonly options: IPlayerOptions;
  private currentTrack: Track | null = null;

  constructor(options: IPlayerOptions, node: Node, logger: ILogger) {
    super();
    
    this.guild = options.guild;
    this.channel = options.channel;
    this.node = node;
    this.options = options;
    this.logger = logger;

    // Initialize service dependencies
    this.voiceManager = new VoiceConnectionManager(logger);
    this.audioManager = new AudioEffectsManager(node, options.guild.id, logger);
    this.queueManager = new QueueManager(
      options.queueType || 'Queue',
      options.maxQueueSize || 100,
      logger
    );
    this.stateManager = new PlayerStateManager(logger);

    this.setupEventHandlers();
    this.initializeAudio();
  }

  // Getters following encapsulation principles
  public get queue(): Queue {
    return this.queueManager as any; // Type compatibility
  }

  public get filters(): Filters {
    return this.audioManager.getFilters();
  }

  public get state(): PlayerState {
    return this.stateManager.getState();
  }

  public get volume(): number {
    return this.audioManager.getVolume();
  }

  public get loop(): LoopType {
    return this.queueManager.getLoopMode();
  }

  public get paused(): boolean {
    return this.stateManager.isPaused();
  }

  public get playing(): boolean {
    return this.stateManager.isPlaying();
  }

  public get connected(): boolean {
    return this.voiceManager.isConnected();
  }

  /**
   * Connect to voice channel
   */
  public async connect(): Promise<void> {
    try {
      this.stateManager.setState(PlayerState.CONNECTING);
      await this.voiceManager.connect(this.channel);
      this.stateManager.setState(PlayerState.STOPPED);
      
      this.logger.info(`Player connected to ${this.channel.name}`, 'player');
    } catch (error) {
      this.stateManager.setState(PlayerState.IDLE);
      throw error;
    }
  }

  /**
   * Disconnect from voice channel
   */
  public async disconnect(): Promise<void> {
    await this.voiceManager.disconnect();
    this.stateManager.setState(PlayerState.IDLE);
    this.logger.info('Player disconnected', 'player');
  }

  /**
   * Play a track or resume playback
   */
  public async play(track?: Track): Promise<void> {
    try {
      if (!this.stateManager.canPlay()) {
        throw new Error(`Cannot play in current state: ${PlayerState[this.state]}`);
      }

      if (track) {
        this.currentTrack = track;
      } else if (!this.currentTrack) {
        this.currentTrack = this.queueManager.getNext();
      }

      if (!this.currentTrack) {
        throw new Error('No track to play');
      }

      await this.sendPlayCommand(this.currentTrack);
      this.stateManager.setState(PlayerState.PLAYING);
      
      this.emit('trackStart', this, this.currentTrack);
      this.logger.info(`Now playing: ${this.currentTrack.title}`, 'player');
      
    } catch (error) {
      this.logger.error('Failed to play track', error as Error, 'player');
      throw error;
    }
  }

  /**
   * Pause playback
   */
  public async pause(): Promise<void> {
    if (!this.stateManager.canPause()) {
      throw new Error(`Cannot pause in current state: ${PlayerState[this.state]}`);
    }

    await this.sendPauseCommand(true);
    this.stateManager.setState(PlayerState.PAUSED);
    this.logger.info('Playback paused', 'player');
  }

  /**
   * Resume playback
   */
  public async resume(): Promise<void> {
    if (!this.stateManager.isPaused()) {
      throw new Error('Player is not paused');
    }

    await this.sendPauseCommand(false);
    this.stateManager.setState(PlayerState.PLAYING);
    this.logger.info('Playback resumed', 'player');
  }

  /**
   * Stop playback
   */
  public async stop(): Promise<void> {
    if (!this.stateManager.canStop()) {
      throw new Error(`Cannot stop in current state: ${PlayerState[this.state]}`);
    }

    await this.sendStopCommand();
    this.stateManager.setState(PlayerState.STOPPED);
    this.currentTrack = null;
    this.logger.info('Playback stopped', 'player');
  }

  /**
   * Skip to next track
   */
  public async skip(): Promise<void> {
    const nextTrack = this.queueManager.getNext();
    if (nextTrack) {
      await this.play(nextTrack);
    } else {
      await this.stop();
      this.emit('queueEnd', this);
    }
  }

  /**
   * Seek to position in current track
   */
  public async seek(position: number): Promise<void> {
    if (!this.currentTrack) {
      throw new Error('No track is currently playing');
    }

    await this.sendSeekCommand(position);
    this.logger.debug(`Seeked to position: ${position}ms`, 'player');
  }

  /**
   * Set volume
   */
  public async setVolume(volume: number): Promise<void> {
    await this.audioManager.setVolume(volume);
    this.logger.info(`Volume set to ${volume}%`, 'player');
  }

  /**
   * Set loop mode
   */
  public setLoop(loop: LoopType): void {
    this.queueManager.setLoopMode(loop);
    this.logger.info(`Loop mode set to: ${LoopType[loop]}`, 'player');
  }

  /**
   * Add track to queue
   */
  public addTrack(track: Track): void {
    this.queueManager.addTrack(track);
  }

  /**
   * Add multiple tracks to queue
   */
  public addTracks(tracks: Track[]): void {
    this.queueManager.addTracks(tracks);
  }

  /**
   * Remove track from queue
   */
  public removeTrack(index: number): Track | null {
    return this.queueManager.removeTrack(index);
  }

  /**
   * Clear queue
   */
  public clearQueue(): void {
    this.queueManager.clear();
  }

  /**
   * Shuffle queue
   */
  public shuffleQueue(): void {
    this.queueManager.shuffle();
  }

  /**
   * Destroy player and cleanup resources
   */
  public async destroy(): Promise<void> {
    try {
      await this.stop();
      await this.disconnect();
      this.stateManager.setState(PlayerState.DESTROYED);
      this.removeAllListeners();
      
      this.logger.info('Player destroyed', 'player');
      this.emit('playerDestroy', this);
      
    } catch (error) {
      this.logger.error('Error during player destruction', error as Error, 'player');
    }
  }

  /**
   * Setup event handlers for services
   */
  private setupEventHandlers(): void {
    // Voice connection events
    this.voiceManager.on('disconnected', () => {
      this.stateManager.setState(PlayerState.IDLE);
      this.emit('voiceConnectionUpdate', this, 'disconnected');
    });

    this.voiceManager.on('error', (error) => {
      this.emit('error', error);
    });

    // State manager events
    this.stateManager.on('stateChange', (newState, oldState) => {
      this.emit('playerUpdate', this, { state: newState, oldState });
    });
  }

  /**
   * Initialize audio settings
   */
  private async initializeAudio(): Promise<void> {
    try {
      const initialVolume = this.options.volume || 100;
      await this.audioManager.setVolume(initialVolume);
    } catch (error) {
      this.logger.error('Failed to initialize audio', error as Error, 'player');
    }
  }

  /**
   * Send play command to Lavalink
   */
  private async sendPlayCommand(track: Track): Promise<void> {
    await this.node.send({
      op: 'play',
      guildId: this.guild.id,
      track: track.track,
      startTime: track.startTime || 0,
      endTime: track.endTime || 0,
    });
  }

  /**
   * Send pause command to Lavalink
   */
  private async sendPauseCommand(paused: boolean): Promise<void> {
    await this.node.send({
      op: 'pause',
      guildId: this.guild.id,
      pause: paused,
    });
  }

  /**
   * Send stop command to Lavalink
   */
  private async sendStopCommand(): Promise<void> {
    await this.node.send({
      op: 'stop',
      guildId: this.guild.id,
    });
  }

  /**
   * Send seek command to Lavalink
   */
  private async sendSeekCommand(position: number): Promise<void> {
    await this.node.send({
      op: 'seek',
      guildId: this.guild.id,
      position,
    });
  }
}

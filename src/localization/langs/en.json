{"commands": {"music": {"play": {"no_tracks": "❌ No tracks found for your query!", "track_added": "✅ Added **{title}** to the queue!", "playlist_added": "📋 Added **{count}** tracks from playlist **{name}**", "now_playing": "🎵 Now playing: **{title}**", "loading": "🔍 Searching for tracks..."}, "pause": {"paused": "⏸️ Music paused!", "already_paused": "❌ Music is already paused!", "vote_registered": "🗳️ Vote to pause registered! ({votes}/{required})"}, "resume": {"resumed": "▶️ Music resumed!", "not_paused": "❌ Music is not paused!", "vote_registered": "🗳️ Vote to resume registered! ({votes}/{required})"}, "skip": {"skipped": "⏭️ Skipped: **{title}**", "skipped_multiple": "⏭️ Skipped **{title}** and {count} more tracks!", "no_track": "❌ No track is currently playing!", "vote_registered": "🗳️ Vote to skip registered! ({votes}/{required})"}, "stop": {"stopped": "⏹️ Music stopped and queue cleared!", "vote_registered": "🗳️ Vote to stop registered! ({votes}/{required})"}, "volume": {"set": "🔊 Volume set to **{volume}%**!", "invalid": "❌ Volume must be between 0 and 100!"}, "nowplaying": {"title": "🎵 Now Playing", "no_track": "❌ No music is currently playing!"}}, "queue": {"list": {"title": "📋 Music Queue", "empty": "❌ The queue is empty!", "page_info": "Page {page}/{total} • {count} tracks • {duration} total"}, "shuffle": {"shuffled": "🔀 Queue shuffled!", "empty": "❌ The queue is empty!", "vote_registered": "🗳️ Vote to shuffle registered! ({votes}/{required})"}, "clear": {"cleared": "🗑️ Cleared **{count}** tracks from the queue!"}, "remove": {"removed": "🗑️ Removed **{title}** from the queue!", "removed_multiple": "🗑️ Removed **{count}** tracks from the queue!", "invalid_position": "❌ Invalid position! Please check the queue and try again.", "no_permission": "❌ You can only remove tracks you requested!"}, "repeat": {"set": "🔁 Repeat mode set to **{mode}**!", "changed": "🔁 Repeat mode changed from **{old}** to **{new}**!"}, "skipto": {"success": "⏭️ Skipped to position **{position}** in the queue!", "invalid_position": "❌ Invalid position! The queue only has {count} tracks."}, "history": {"title": "🎵 Your Listening History", "empty": "❌ You have no listening history!"}}, "effects": {"bassboost": {"applied": "🎵 Bass boost effect applied! (Level: {level})"}, "nightcore": {"applied": "🎵 Nightcore effect applied! (Speed: {speed}x, Pitch: {pitch}x)"}, "vaporwave": {"applied": "🎵 Vaporwave effect applied! (Speed: {speed}x, Pitch: {pitch}x)"}, "eightd": {"applied": "🎵 8D audio effect applied! (Rotation speed: {speed}Hz)"}, "karaoke": {"applied": "🎵 Karaoke effect applied! (Level: {level})"}, "tremolo": {"applied": "🎵 Tremolo effect applied! (Frequency: {frequency}Hz, Depth: {depth})"}, "cleared": "🎵 All audio effects cleared!", "failed": "❌ Failed to apply {effect} effect!", "status": {"title": "🎵 Active Audio Effects", "none": "🎵 No audio effects are currently active.", "active": "{count} effect(s) active"}}, "settings": {"prefix": {"current": "🔧 Current prefix: `{prefix}`", "updated": "🔧 Prefix updated to: `{prefix}`"}, "language": {"current": "🌐 Current language: **{language}**", "updated": "🌐 Language updated to: **{language}**"}, "music_channel": {"set": "🎵 Music request channel set to {channel}!", "removed": "🎵 Music request channel removed!"}, "controller": {"enabled": "🎛️ Controller messages enabled!", "disabled": "🎛️ Controller messages disabled!"}, "connect": {"success": "🔗 Connected to {channel}!"}, "disconnect": {"success": "🔌 Disconnected from **{channel}**!"}, "info": {"title": "🤖 V<PERSON><PERSON>"}, "ping": {"title": "🏓 Pong!"}, "view": {"title": "🔧 Server Settings"}}}, "errors": {"no_permission": "❌ You don't have permission to use this command!", "not_in_voice": "❌ You must be in a voice channel to use this command!", "not_same_voice": "❌ You must be in {channel} to use this command!", "no_player": "❌ No music player is active!", "no_track": "❌ No track is currently playing!", "dj_only": "❌ You need DJ permissions to use this command!", "bot_permissions": "❌ I need {permissions} permissions in that voice channel!", "command_error": "❌ An error occurred while executing the command!", "invalid_url": "❌ Invalid URL provided!", "track_load_failed": "❌ Failed to load track!", "player_error": "❌ An error occurred with the music player!", "guild_only": "❌ This command can only be used in servers!", "manage_server_required": "❌ You need the \"Manage Server\" permission to change settings!", "manage_channels_required": "❌ You need to be in the voice channel or have \"Manage Channels\" permission!", "queue_empty": "❌ The queue is empty!", "queue_full": "❌ Queue is full! Maximum size: {max}", "invalid_position": "❌ Invalid position! Please check the queue and try again.", "no_results": "❌ No results found for your search!", "connection_failed": "❌ Failed to connect to voice channel!", "already_connected": "❌ Bot is already connected to a voice channel!", "not_connected": "❌ Bot is not connected to any voice channel!"}, "general": {"yes": "Yes", "no": "No", "enabled": "Enabled", "disabled": "Disabled", "unknown": "Unknown", "none": "None", "loading": "Loading...", "success": "Success!", "error": "Error!", "cancelled": "Cancelled", "timeout": "Timed out", "page": "Page", "of": "of", "total": "total", "duration": "Duration", "position": "Position", "volume": "Volume", "repeat": "Repeat", "queue": "Queue", "tracks": "tracks", "track": "track", "artist": "Artist", "requested_by": "Requested by", "now_playing": "Now Playing", "not_set": "Not set"}, "time": {"seconds": "seconds", "minutes": "minutes", "hours": "hours", "days": "days", "weeks": "weeks", "months": "months", "years": "years", "second": "second", "minute": "minute", "hour": "hour", "day": "day", "week": "week", "month": "month", "year": "year"}, "controller": {"title": "🎵 Music Controller", "inactive_title": "🎵 Music Player", "inactive_description": "No music is currently playing", "vote_pause": "🗳️ Vote to pause registered! ({votes}/{required})", "vote_resume": "🗳️ Vote to resume registered! ({votes}/{required})", "vote_skip": "🗳️ Vote to skip registered! ({votes}/{required})", "vote_stop": "🗳️ Vote to stop registered! ({votes}/{required})", "vote_shuffle": "🗳️ Vote to shuffle registered! ({votes}/{required})", "not_in_channel": "❌ You must be in {channel} to use the controller!", "dj_required": "❌ You need DJ permissions to use this feature!", "no_track": "❌ No track is currently playing!", "queue_empty": "❌ The queue is empty!", "coming_soon": "Coming soon!"}, "search": {"title": "🔍 Search Results", "no_selection": "❌ No tracks selected!", "no_tracks": "❌ No tracks available!", "cancelled": "❌ Search cancelled.", "added_selected": "✅ Added **{count}** selected tracks to the queue!", "added_all": "✅ Added **{count}** tracks to the queue!", "failed": "❌ Failed to add tracks to queue!"}, "help": {"title": "🤖 Vocard Help", "overview": {"title": "Overview", "description": "Bot overview and quick start guide"}, "categories": {"music": "Basic music playback and control commands", "queue": "Commands for managing the music queue", "effects": "Commands for applying audio effects and filters", "settings": "Commands for configuring bot settings"}}}
/**
 * MIT License
 * 
 * Copyright (c) 2025 NirrussVn0
 */

import { container } from 'tsyringe';
import { ILogger } from './Logger';
import { IDatabaseConnection, IGuildSettingsRepository, IUserRepository } from '../interfaces/IRepository';
import { IDiscordConfig, IDatabaseConfig, IBotConfig } from '../interfaces/ISettings';

/**
 * Service container for dependency injection following DIP
 * Provides centralized service registration and resolution
 */
export class ServiceContainer {
  private static instance: ServiceContainer;

  private constructor() {}

  public static getInstance(): ServiceContainer {
    if (!ServiceContainer.instance) {
      ServiceContainer.instance = new ServiceContainer();
    }
    return ServiceContainer.instance;
  }

  /**
   * Register core services
   */
  public registerCoreServices(
    logger: ILogger,
    discordConfig: IDiscordConfig,
    databaseConfig: IDatabaseConfig,
    botConfig: IBotConfig
  ): void {
    container.registerInstance('Logger', logger);
    container.registerInstance('DiscordConfig', discordConfig);
    container.registerInstance('DatabaseConfig', databaseConfig);
    container.registerInstance('BotConfig', botConfig);
  }

  /**
   * Register database services
   */
  public registerDatabaseServices(
    connection: IDatabaseConnection,
    settingsRepo: IGuildSettingsRepository,
    userRepo: IUserRepository
  ): void {
    container.registerInstance('DatabaseConnection', connection);
    container.registerInstance('GuildSettingsRepository', settingsRepo);
    container.registerInstance('UserRepository', userRepo);
  }

  /**
   * Register client instance
   */
  public registerClient(client: any): void {
    container.registerInstance('Client', client);
  }

  /**
   * Resolve service by token
   */
  public resolve<T>(token: string): T {
    return container.resolve<T>(token);
  }

  /**
   * Check if service is registered
   */
  public isRegistered(token: string): boolean {
    try {
      container.resolve(token);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Clear all registrations (for testing)
   */
  public clear(): void {
    container.clearInstances();
  }
}

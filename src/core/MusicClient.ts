/**
 * MIT License
 * 
 * Copyright (c) 2025 NirrussVn0
 */

import 'reflect-metadata';
import { GatewayIntentBits } from 'discord.js';
import { Client as DiscordXClient } from '@discordx/discordx';
import { IDiscordClient, IServiceInitializer } from '../interfaces/IClient';
import { ILogger, LoggerFactory } from './Logger';
import { ServiceContainer } from './ServiceContainer';
import { ConfigurationService } from '../services/ConfigurationService';
import { DatabaseService } from '../services/DatabaseService';
import { EventHandlerService } from '../services/EventHandlerService';
import { CommandService } from '../services/CommandService';

/**
 * Refactored MusicClient following SOLID principles
 * Now focuses only on Discord client coordination
 */
export class MusicClient extends DiscordXClient implements IDiscordClient, IServiceInitializer {
  private logger!: ILogger;
  private serviceContainer: ServiceContainer;
  private configService!: ConfigurationService;
  private databaseService!: DatabaseService;
  private eventHandlerService!: EventHandlerService;
  private commandService!: CommandService;

  constructor() {
    super({
      intents: [
        GatewayIntentBits.Guilds,
        GatewayIntentBits.GuildMessages,
        GatewayIntentBits.GuildVoiceStates,
        GatewayIntentBits.MessageContent,
        GatewayIntentBits.GuildMembers,
      ],
      silent: false,
      simpleCommand: {
        prefix: '?', // Will be updated from settings
      },
    });

    this.serviceContainer = ServiceContainer.getInstance();
  }

  /**
   * Initialize all services following DIP
   */
  public async initializeServices(): Promise<void> {
    try {
      // Initialize configuration service
      this.configService = new ConfigurationService();
      await this.configService.loadConfiguration();

      // Initialize logger
      const loggerFactory = new LoggerFactory();
      this.logger = loggerFactory.createLogger(this.configService.getLoggingConfig());

      // Initialize database service
      this.databaseService = new DatabaseService(
        this.configService.getDatabaseConfig(),
        this.logger
      );

      // Initialize event handler service
      this.eventHandlerService = new EventHandlerService(
        this,
        this.logger,
        this.configService.getBotConfig()
      );

      // Initialize command service
      this.commandService = new CommandService(this, this.logger);

      // Register services in container
      this.registerServices();

      this.logger.info('Services initialized successfully', 'client');
    } catch (error) {
      console.error('Failed to initialize services:', error);
      process.exit(1);
    }
  }

  /**
   * Register services in DI container
   */
  public registerServices(): void {
    this.serviceContainer.registerCoreServices(
      this.logger,
      this.configService.getDiscordConfig(),
      this.configService.getDatabaseConfig(),
      this.configService.getBotConfig()
    );

    if (this.databaseService.isConnected()) {
      this.serviceContainer.registerDatabaseServices(
        this.databaseService.getConnection(),
        this.databaseService.getGuildSettingsRepository(),
        this.databaseService.getUserRepository()
      );
    }

    this.serviceContainer.registerClient(this);
  }

  /**
   * Check if client is ready
   */
  public isReady(): boolean {
    return this.readyAt !== null;
  }

  /**
   * Shutdown the client gracefully
   */
  public async shutdown(): Promise<void> {
    this.logger.info('Shutting down bot...', 'client');
    
    if (this.databaseService) {
      await this.databaseService.close();
    }
    
    this.destroy();
    this.logger.info('Bot shutdown complete', 'client');
  }

  public async start(): Promise<void> {
    try {
      // Initialize services first
      await this.initializeServices();

      // Connect to database
      await this.databaseService.initialize();
      this.logger.info('Database connected successfully', 'client');

      // Set up event handlers
      this.eventHandlerService.setupEventHandlers();

      // Import commands and events
      await this.commandService.importCommands();

      // Login to Discord
      const discordConfig = this.configService.getDiscordConfig();
      await this.login(discordConfig.token);

      this.logger.info('Bot started successfully', 'client');
    } catch (error) {
      this.logger.error('Failed to start bot', error as Error, 'client');
      process.exit(1);
    }
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  const serviceContainer = ServiceContainer.getInstance();
  if (serviceContainer.isRegistered('Client')) {
    const client = serviceContainer.resolve<MusicClient>('Client');
    await client.shutdown();
  }
  process.exit(0);
});

process.on('SIGTERM', async () => {
  const serviceContainer = ServiceContainer.getInstance();
  if (serviceContainer.isRegistered('Client')) {
    const client = serviceContainer.resolve<MusicClient>('Client');
    await client.shutdown();
  }
  process.exit(0);
});

process.on('unhandledRejection', (reason) => {
  console.error('Unhandled Rejection at:', reason);
});

process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  process.exit(1);
});

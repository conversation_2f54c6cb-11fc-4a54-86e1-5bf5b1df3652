# Discord Bot Configuration
DISCORD_TOKEN=your_discord_bot_token_here
DISCORD_CLIENT_ID=your_discord_client_id_here

# MongoDB Configuration
MONGODB_URL=*************************************
MONGODB_NAME=vocard

# Lavalink Configuration
LAVALINK_HOST=localhost
LAVALINK_PORT=2333
LAVALINK_PASSWORD=youshallnotpass
LAVALINK_SECURE=false

# External APIs
GENIUS_TOKEN=your_genius_token_here

# Bot Configuration
BOT_PREFIX=?
EMBED_COLOR=0xb3b3b3
MAX_QUEUE_SIZE=1000
LYRICS_PLATFORM=lrclib

# IPC Configuration (Dashboard)
IPC_ENABLE=false
IPC_HOST=127.0.0.1
IPC_PORT=8000
IPC_PASSWORD=your_ipc_password_here
IPC_SECURE=false

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_ENABLE=true
LOG_FILE_PATH=./logs
LOG_MAX_HISTORY=30

# Development
NODE_ENV=production
